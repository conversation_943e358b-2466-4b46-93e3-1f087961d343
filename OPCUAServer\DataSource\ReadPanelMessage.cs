using System.Collections.Concurrent;
using System.ComponentModel.DataAnnotations;
using Newtonsoft.Json.Linq;
using OPCUAServer.Common;
using OPCUAServer.Utilites;
using Serilog;

namespace PanelHttp;

/// <summary>
/// Container class for device message data indexed by device ID
/// </summary>
public class DeviceMessageData
{
    public List<MessageInfo> ListMessages { get; set; } = new();
}

internal class ReadPanelMessage
{
    private const int TIMERINTERVAL = 2000;
    private readonly ILogger _logger = LoggingHelper.GetLogger<ReadPanelMessage>();

    // Timer-related fields for overlap prevention
    private Timer? _messageTimer;
    private volatile bool _isTimerExecuting = false;

    public static List<MessageInfo> DeviceMessages { get; } = new();

    /// <summary>
    /// Dictionary to store device message data indexed by device ID
    /// This is used by PanelHistoricalMsgRealtimeData for compatibility
    /// </summary>
    public static ConcurrentDictionary<string, DeviceMessageData> DeviceMessageItem { get; } = new();

    private long LastStartTime { get; set; } = DateTimeOffset.UtcNow.ToUnixTimeSeconds() - TIMERINTERVAL;

    // private long AlarmEndTime { get; set; } = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

    /// <summary>
    /// Starts the timer-based message reading system.
    /// This method implements a timer that calls StartReadUdcMessageAsync every TIMERINTERVAL milliseconds.
    /// </summary>
    /// <param name="iP">Device IP address</param>
    /// <param name="port">Device port</param>
    /// <param name="token">Authentication token</param>
    public void StartReadUdcMessage(string iP, string port, string token)
    {
        _logger.Information($"Starting timer-based message reading for device {iP}:{port}");

        // Initialize the timer following the same pattern as ReadPanelHttp.StartReadUdcHttp
        _messageTimer = new Timer(
            async state =>
            {
                // Timer overlap prevention: skip execution if previous timer is still running
                if (_isTimerExecuting)
                {
                    _logger.Debug("Skipping timer execution - previous execution still in progress");
                    return;
                }

                try
                {
                    _isTimerExecuting = true;
                    _logger.Debug($"Timer executing - reading messages from {iP}:{port}");

                    // Call the async message reading method
                    await StartReadUdcMessageAsync(iP, port, token);

                    // Update AlarmStartTime after successful execution to fetch new messages in next interval
                    // _logger.Debug($"Updated AlarmStartTime to {LastStartTime}");
                }
                catch (Exception ex)
                {
                    // Handle exceptions to prevent timer crashes
                    _logger.Error(ex, $"Timer execution failed for device {iP}:{port}: {ex.Message}");
                }
                finally
                {
                    _isTimerExecuting = false;

                    // Schedule next timer execution (one-shot timer pattern)
                    _messageTimer?.Change(TimeSpan.FromMilliseconds(TIMERINTERVAL), TimeSpan.FromMilliseconds(-1));
                }
            },
            null,
            TimeSpan.FromMilliseconds(-1), // Don't start immediately
            TimeSpan.FromMilliseconds(-1)); // One-shot timer

        // Start the first timer execution
        _messageTimer?.Change(TimeSpan.FromMilliseconds(TIMERINTERVAL), TimeSpan.FromMilliseconds(-1));
        _logger.Information($"Timer started with {TIMERINTERVAL}ms interval");
    }

    /// <summary>
    /// Stops the timer-based message reading system.
    /// </summary>
    public void StopReadUdcMessage()
    {
        try
        {
            _messageTimer?.Change(Timeout.Infinite, Timeout.Infinite);
            _logger.Information("Message reading timer stopped");
        }
        catch (Exception ex)
        {
            _logger.Error(ex, $"Error stopping message timer: {ex.Message}");
        }
    }

    /// <summary>
    /// Disposes the timer resources.
    /// </summary>
    public void Dispose()
    {
        StopReadUdcMessage();
        _messageTimer?.Dispose();
        _messageTimer = null;
        _logger.Information("ReadPanelMessage disposed");
    }

    private async Task StartReadUdcMessageAsync(string iP, string port, string token)
    {
        try
        {
            string messageJson = await DeviceHistoricalMessageAsync(iP, port, token);
            DeviceMessages.AddRange(MessageResultAnalyze(messageJson));
        }
        catch (Exception ex)
        {
            // Log the exception, we don't want a failed message update to crash the timer.
            _logger.Error($"Failed to retrieve or parse device messages: {ex.Message}");
        }
    }

    private async Task<string> DeviceHistoricalMessageAsync(string iP, string port, string accessToken, string page = "1", string pageSize = "50")
    {
        using HttpClient client = new();
        client.DefaultRequestHeaders.Add("accept", "text/plain");
        client.DefaultRequestHeaders.Add("Authorization", $"Bearer {accessToken}");

        long now = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
        var queryParams = $"page={page}&pageSize={pageSize}&alarmStartTime={LastStartTime}&alarmEndTime={now}";
        LastStartTime = now;

        string apiUrl = $"http://{iP}:{port}/api/v1/Alarm?{queryParams}";
        var response = await client.GetAsync(apiUrl);
        response.EnsureSuccessStatusCode();

        return await response.Content.ReadAsStringAsync();
    }

    private List<MessageInfo> MessageResultAnalyze(string messageJson)
    {
        var res = new List<MessageInfo>();

        if (string.IsNullOrEmpty(messageJson))
        {
            return res;
        }

        var jObject = JObject.Parse(messageJson);

        if (jObject.TryGetValue("items", out var itemsToken) && itemsToken is JArray jArrayObj)
        {
            foreach (JToken jsonitem in jArrayObj)
            {
                if (jsonitem is JObject job)
                {
                    // Safely get each property value.
                    job.TryGetValue("id", StringComparison.OrdinalIgnoreCase, out var oidToken);
                    job.TryGetValue("severity", StringComparison.OrdinalIgnoreCase, out var severityToken);
                    job.TryGetValue("logTime", StringComparison.OrdinalIgnoreCase, out var timestampToken);
                    job.TryGetValue("alarmInfo", StringComparison.OrdinalIgnoreCase, out var textToken);

                    var messageInfo = new MessageInfo
                    {
                        Oid = oidToken?.ToString() ?? string.Empty,
                        Severity = int.TryParse(severityToken?.ToString(), out var severityInt) ? severityInt : 0,
                        Timestamp = DateTimeOffset.TryParse(timestampToken?.ToString(), out var parsedTimestamp) ? parsedTimestamp : DateTimeOffset.UtcNow,
                        MessageText = textToken?.ToString() ?? string.Empty,
                    };

                    _logger.Information($"Device messages:{messageInfo} added successfully");
                    if (MessageInfo.ValidateMessageInfo(messageInfo))
                    {
                        res.Add(messageInfo);
                    }
                }
            }
        }

        return res;
    }
}
