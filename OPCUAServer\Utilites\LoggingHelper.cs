using Serilog;

namespace OPCUAServer.Utilites;

/// <summary>
/// Log Helper class that provides convenient logging methods
/// </summary>
public static class LoggingHelper
{
    /// <summary>
    /// Create a logger for the specified type.
    /// </summary>
    /// <typeparam name="T">Type.</typeparam>
    /// <returns>Logger.</returns>
    public static ILogger GetLogger<T>()
    {
        return Log.ForContext<T>();
    }

    /// <summary>
    /// Create a logger for the specified type name.
    /// </summary>
    /// <param name="typeName">Type name.</param>
    /// <returns>Logger.</returns>
    public static ILogger GetLogger(string typeName)
    {
        return Log.ForContext("SourceContext", typeName);
    }

    /// <summary>
    /// Log operation start.
    /// </summary>
    /// <param name="logger">Logger.</param>
    /// <param name="operationName">Operation name.</param>
    /// <param name="parameters">Parameters.</param>
    public static void LogOperationStart(this ILogger logger, string operationName, object? parameters = null)
    {
        if (parameters != null)
        {
            logger.Information("Starting Operation: {OperationName}, Parameters: {@Parameters}", operationName, parameters);
        }
        else
        {
            logger.Information("Starting Operation: {OperationName}", operationName);
        }
    }

    /// <summary>
    /// Log operation success.
    /// </summary>
    /// <param name="logger">Logger.</param>
    /// <param name="operationName">Operation name.</param>
    /// <param name="duration">Duration (milliseconds).</param>
    /// <param name="result">Result.</param>
    public static void LogOperationSuccess(this ILogger logger, string operationName, long? duration = null, object? result = null)
    {
        var message = "Operation Completed Successfully: {OperationName}";
        var args = new List<object> { operationName };

        if (duration.HasValue)
        {
            message += ", Duration: {Duration}ms";
            args.Add(duration.Value);
        }

        if (result != null)
        {
            message += ", Result: {@Result}";
            args.Add(result);
        }

        logger.Information(message, args.ToArray());
    }

    /// <summary>
    /// Log operation failure.
    /// </summary>
    /// <param name="logger">Logger.</param>
    /// <param name="operationName">Operation name.</param>
    /// <param name="exception">Exception.</param>
    /// <param name="duration">Duration (milliseconds).</param>
    public static void LogOperationFailure(this ILogger logger, string operationName, Exception exception, long? duration = null)
    {
        var message = "Operation Failed: {OperationName}";
        var args = new List<object> { operationName };

        if (duration.HasValue)
        {
            message += ", Duration: {Duration}ms";
            args.Add(duration.Value);
        }

        logger.Error(exception, message, args.ToArray());
    }

    /// <summary>
    /// Log performance information.
    /// </summary>
    /// <param name="logger">Logger.</param>
    /// <param name="operationName">Operation name.</param>
    /// <param name="duration">Duration (milliseconds).</param>
    /// <param name="additionalData">Additional data.</param>
    public static void LogPerformance(this ILogger logger, string operationName, long duration, object? additionalData = null)
    {
        if (additionalData != null)
        {
            logger.Information("Performance Statistics - Operation: {OperationName}, Duration: {Duration}ms, Additional Data: {@AdditionalData}", operationName, duration, additionalData);
        }
        else
        {
            logger.Information("Performance Statistics - Operation: {OperationName}, Duration: {Duration}ms", operationName, duration);
        }
    }

    /// <summary>
    /// Log connection status change.
    /// </summary>
    /// <param name="logger">Logger.</param>
    /// <param name="connectionType">Connection type.</param>
    /// <param name="status">Status.</param>
    /// <param name="endpoint">Endpoint.</param>
    public static void LogConnectionStatus(this ILogger logger, string connectionType, string status, string? endpoint = null)
    {
        if (!string.IsNullOrEmpty(endpoint))
        {
            logger.Information(
                "Connection Status Changed - Type: {ConnectionType}, Status: {Status}, Endpoint: {Endpoint}", connectionType, status, endpoint);
        }
        else
        {
            logger.Information("Connection Status Changed - Type: {ConnectionType}, Status: {Status}", connectionType, status);
        }
    }
}