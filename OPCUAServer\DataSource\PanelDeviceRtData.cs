using System.Net.Http.Headers;
using Newtonsoft.Json.Linq;

namespace PanelHttp;

internal class PanelDeviceRtData
{
    /// <summary>
    /// Asynchronously retrieves real-time data for a single device.
    /// This method is now asynchronous and will throw an exception on HTTP failure.
    /// </summary>
    /// <param name="iP">The IP address of the server.</param>
    /// <param name="port">The port number of the server.</param>
    /// <param name="token">The authorization token.</param>
    /// <param name="id">The ID of the device.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the device data as a string.</returns>
    /// <exception cref="HttpRequestException">Thrown when the HTTP request fails (e.g., non-success status code).</exception>
    public static async Task<string> SingleDeviceRealtimeDataAsync(string iP, string port, string token, string id)
    {
        using HttpClient client = new HttpClient();
        string apiUrl = $"http://{iP}:{port}/api/v1/status/Currently/" + id;

        client.DefaultRequestHeaders.Add("accept", "text/plain");
        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
        try
        {
            HttpResponseMessage response = await client.GetAsync(apiUrl);
            response.EnsureSuccessStatusCode();
            return await response.Content.ReadAsStringAsync();
        }
        catch (HttpRequestException e)
        {
            Console.WriteLine($"Request failed for device {id}: {e.Message}");
            throw;
        }
        catch (Exception e)
        {
            Console.WriteLine($"An unexpected exception occurred while fetching data for device {id}: {e.Message}");
            throw;
        }
    }

    public static UARealtimeData DataPoints2RealtimeData(DataPoints obj)
    {
        var uARtData = new UARealtimeData()
        {
            Item_id = obj.ItemId,
            Device_Name = obj.ItemName,
            IP = obj.ItemId,
            TimeStamp = DateTime.Now.ToLocalTime().ToString(),
            Channel_count = obj.ListChannel.Count().ToString(),
            ChannelList = new List<ChannelVal>()
        };

        for (int i = 0; i < obj.ListChannel.Count; i++)
        {
            var itemObj = new ChannelVal()
            {
                Name = string.Empty,
                Id = i + 1,
                Val = string.Empty,
                Valid = true
            };
            uARtData.ChannelList.Add(itemObj);
        }

        for (int i = 0; i < obj.ListChannel.Count; i++)
        {
            string tempName = obj.ListChannel[i].Internal_name;
            uARtData.ChannelList[i].Name = tempName;
            uARtData.ChannelList[i].Id = i + 1;
            uARtData.ChannelList[i].Val = obj.ListChannel[i].Display_value;
            uARtData.ChannelList[i].Valid = true;
        }

        return uARtData;
    }

    public async Task<UARealtimeData?> ReadPanelDeviceDataAsync(string iP, string port, string token, string deviceId, string deviceType)
    {
        try
        {
            string getArchiveRes = await SingleDeviceRealtimeDataAsync(iP, port, token, deviceId);
            var dataPoints = PanelDataPointsResultAnalyze(getArchiveRes, deviceId, deviceType);
            var deviceRtData = DataPoints2RealtimeData(dataPoints); // used for opc ua server
            return deviceRtData;
        }
        catch (Exception ex)
        {
            // If fetching data fails, log the error and return null.
            Console.WriteLine($"Failed to process real-time data for device {deviceId}: {ex.Message}");
            return null;
        }
    }

    private static DataPoints PanelDataPointsResultAnalyze(string dataJson, string itemId, string deviceName)
    {
        DataPoints dataPointsObj = new()
        {
            ItemId = itemId,
            ItemName = deviceName,
            ListChannel = new List<IChannel>()
        };
        JObject jsonObject = JObject.Parse(dataJson);

        // Use TryGetValue for safe access and check if the token is a JObject
        if (jsonObject.TryGetValue("data", out JToken? sensorDataToken) && sensorDataToken is JObject sensorDataObject)
        {
            foreach (JProperty property in sensorDataObject.Properties())
            {
                dataPointsObj.ListChannel.Add(new ChannelInfo
                {
                    Internal_name = property.Name.ToString(),
                    Display_name = property.Name.ToString(),
                    Display_value = property.Value.ToString()
                });
            }
        }

        return dataPointsObj;
    }
}
