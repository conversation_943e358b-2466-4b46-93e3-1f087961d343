/******************************************************************************
** Copyright (c) 2006-2022 Unified Automation GmbH All rights reserved.
**
** Software License Agreement ("SLA") Version 2.8
**
** Unless explicitly acquired and licensed from Licensor under another
** license, the contents of this file are subject to the Software License
** Agreement ("SLA") Version 2.8, or subsequent versions
** as allowed by the SLA, and You may not copy or use this file in either
** source code or executable form, except in compliance with the terms and
** conditions of the SLA.
**
** All software distributed under the SLA is provided strictly on an
** "AS IS" basis, WITHOUT WARRANTY OF ANY KIND, EITHER EXPRESS OR IMPLIED,
** AND LICENSOR HEREBY DISCLAIMS ALL SUCH WARRANTIES, INCLUDING WITHOUT
** LIMITATION, ANY WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR
** PURPOSE, QUIET ENJOYMENT, OR NON-INFRINGEMENT. See the SLA for specific
** language governing rights and limitations under the SLA.
**
** Project: .NET based OPC UA Client Server SDK
**
** Description: OPC Unified Architecture Software Development Kit.
**
** The complete license agreement can be found here:
** http://unifiedautomation.com/License/SLA/2.8/
******************************************************************************/

using Microsoft.Extensions.DependencyInjection;
using OPCUAServer.Common;
using OPCUAServer.Utilites;
using Serilog;
using UnifiedAutomation.UaBase;

namespace OPCUAServer;

/// <summary>
/// OPC UA Server
/// </summary>
public partial class OpcUAServer
{
    private static readonly ILogger _logger = LoggingHelper.GetLogger<OpcUAServer>();

    public static void StartOpcUAServer()
    {
        try
        {
            _logger.Information("Starting OPC UA Server...");
            System.Reflection.Assembly assembly;
#if NETFRAMEWORK
            assembly = System.Reflection.Assembly.GetExecutingAssembly();
#else
            assembly = PlatformUtils.GetAssembly(typeof(OpcUAServer));
#endif

            // The license file must be loaded from an embedded resource.
            _logger.Information("Loading License File...");
            ApplicationLicenseManager.AddProcessLicenses(assembly, "License.lic");
            _logger.Information("License File Loaded Successfully");

            // Start the server.
            _logger.Information("Creating OPCServerManager Instance...");
            var server = Program.ServiceProvider.GetRequiredService<OPCServerManager>();
            _logger.Information("OPCServerManager Instance Created Successfully");

            // ***********************************************************************
            // The following function can be called to configure the server from code
            // This will disable the configuration settings from app.config file
            // ***********************************************************************
            ApplicationInstanceBase application;

#if NETFRAMEWORK
            application = ApplicationInstance.Default;
            // Setting the SecurityProvider is not required to be able to create certificates,
            // since WindowsSecurityProvider is created implicitly by ApplicationInstance class.
#else
            application = ApplicationInstanceBase.Default;

            ConfigureOpcUaApplicationFromCode();
            SetUserIdentityToServerSettings(application);

            // Setting the SecurityProvider is required to be able to create certificates.
            application.SecurityProvider = new BouncyCastleSecurityProvider();
#endif
            application.UntrustedCertificate += Application_UntrustedCertificate;

            // Set event handler for modifying the ApplicationSettings after loading.
            application.ApplicationSettingsLoaded += Application_ApplicationSettingsLoaded;
            application.AutoCreateCertificate = true;

            _logger.Information("Starting OPC UA Application...");
            application.Start(server, null, server);
            _logger.Information("OPC UA Application Started Successfully");

            // Print endpoints for information.
            PrintEndpoints(server);

            // Block until the server exits.
            _logger.Information("Server Started, Press Enter to Exit...");
            Console.ReadLine();

            // Stop the server.
            _logger.Information("Stopping Server...");
            server.Stop();
            _logger.Information("Server Stopped Successfully");
        }
        catch (Exception e)
        {
            _logger.Error(e, "Error Occurred During Server Operation: {ErrorMessage}", e.Message);
        }
    }

    private static void Application_UntrustedCertificate(object sender, UntrustedCertificateEventArgs e)
    {
        _logger.Warning($"Unrecognized Certificate: {e.Certificate.CommonName}, {e.Certificate.SubjectName}");
        _logger.Information(
            $"To accept this certificate, please move the certificate {e.Certificate.CommonName} [{e.Certificate.Thumbprint}].der from {e.Application.RejectedStore.StorePath}\\certs to {e.Application.TrustedStore.StorePath}\\certs");

        _logger.Information(
            $"Move certificate {e.Certificate.CommonName} [{e.Certificate.Thumbprint}].der from {e.Application.RejectedStore.StorePath}\\certs to {e.Application.TrustedStore.StorePath}\\certs to accept");
    }

    /// <summary>
    /// Prints the available EndpointDescriptions to the command line.
    /// </summary>
    /// <param name="server">The server to print the endpoints for.</param>
    private static void PrintEndpoints(OPCServerManager server)
    {
        // print the endpoints.
        _logger.Information("Listening at the following endpoints:");

        foreach (EndpointDescription endpoint in server.Application.Endpoints)
        {
            StatusCode error = server.Application.GetEndpointStatus(endpoint);
            _logger.Information("   {0}: Status={1}", endpoint, error.ToString(true));
        }
    }

    /// <summary>
    /// Adds signed endpoints to the application.
    /// </summary>
    /// <param name="application">The application to add the signed endpoints to.</param>
    private static void AddSigned(ApplicationInstanceBase application)
    {
        var endpointsToAdd = new List<EndpointDescription>();

        foreach (EndpointDescription endpoint in application.Endpoints.ToList())
        {
            if (endpoint.SecurityPolicyUri == SecurityProfiles.None)
            {
                endpointsToAdd.Add(endpoint);
            }

            // Preserve the configuration of the original endpoint
            if (endpoint.SecurityPolicyUri == SecurityProfiles.Basic256Sha256)
            {
                var newEndpoint_sign = (EndpointDescription)endpoint.Clone();
                newEndpoint_sign.SecurityMode = MessageSecurityMode.Sign;
                endpointsToAdd.Add(newEndpoint_sign);

                var newEndpoint_SignAndEncrypt = (EndpointDescription)endpoint.Clone();
                newEndpoint_SignAndEncrypt.SecurityMode = MessageSecurityMode.SignAndEncrypt;
                endpointsToAdd.Add(newEndpoint_SignAndEncrypt);
            }
        }

        application.Endpoints = endpointsToAdd;
    }
}

