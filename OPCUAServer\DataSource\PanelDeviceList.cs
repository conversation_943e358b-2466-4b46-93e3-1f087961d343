using System.Net.Http.Headers;
using Newtonsoft.Json.Linq;

namespace PanelHttp;

internal class PanelDeviceList
{
    private static readonly HashSet<string> SupportedDevices = new(StringComparer.OrdinalIgnoreCase)
    {
        "PAC1020", "PAC1200", "PAC1500", "PAC1600",
        "PAC2200", "PAC3100", "PAC3120", "PAC3200", "PAC3200T", "PAC3220", "PAC4200",
        "Panel", "Substation", "WTMS", "3WA", "3VA", "3WL", "Circuit"
    };

    public async Task<List<IDeviceInfo>> ScanPanelDeviceListAsync(string iP, string port, string token)
    {
        try
        {
            string deviceListJson = await GetDeviceListJsonAsync(iP, port, token);
            return PanelDeviceListResultAnalyze(deviceListJson);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Failed to retrieve or parse device list: {ex.Message}");
            return new List<IDeviceInfo>();
        }
    }

    public List<IDeviceInfo> ScanPanelDeviceList(string iP, string port, string token)
    {
        // Maintain backward compatibility by providing a synchronous wrapper
        return this.ScanPanelDeviceListAsync(iP, port, token).GetAwaiter().GetResult();
    }

    private static async Task<string> GetDeviceListJsonAsync(string iP, string port, string token)
    {
        string apiUrl = $"http://{iP}:{port}/api/v1/Asset/search?levels=Device";

        using var client = new HttpClient();
        client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("text/plain"));
        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);

        var response = await client.GetAsync(apiUrl);
        response.EnsureSuccessStatusCode();

        return await response.Content.ReadAsStringAsync();
    }

    private static bool IsUdcSupported(string deviceName)
    {
        return !string.IsNullOrEmpty(deviceName) && SupportedDevices.Contains(deviceName);
    }

    private static List<IDeviceInfo> PanelDeviceListResultAnalyze(string res)
    {
        var deviceList = new List<IDeviceInfo>();

        if (string.IsNullOrEmpty(res))
        {
            return deviceList;
        }

        var jObject = JObject.Parse(res);

        if (jObject.TryGetValue("count", out var countToken))
        {
            Console.WriteLine("Device count = : " + countToken.ToString());
        }

        if (jObject.TryGetValue("items", out var itemsToken) && itemsToken is JArray jArrayObj)
        {
            foreach (var jsonitem in jArrayObj)
            {
                if (jsonitem is JObject job)
                {
                    if (job.TryGetValue("assetModel", out JToken? typeNameToken))
                    {
                        string typeNameStr = typeNameToken?.ToString() ?? string.Empty;

                        if (IsUdcSupported(typeNameStr))
                        {
                            job.TryGetValue("id", out JToken? idToken);

                            var deviceInfo = new DeviceInfo
                            {
                                DeviceType = typeNameStr,
                                ItemId = idToken?.ToString() ?? string.Empty,
                                DeviceIP = idToken?.ToString() ?? string.Empty
                            };
                            deviceList.Add(deviceInfo);
                        }
                    }
                }
            }
        }

        return deviceList;
    }
}